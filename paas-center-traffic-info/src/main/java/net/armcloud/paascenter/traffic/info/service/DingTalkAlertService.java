package net.armcloud.paascenter.traffic.info.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉预警消息服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DingTalkAlertService {

    @Resource
    private RestTemplate restTemplate;

    @Value("${dingtalk.webhook.url:https://oapi.dingtalk.com/robot/send?access_token=e7c385767be13576698021476e52a089b293235a524b7bd731678b6368e62420}")
    private String dingTalkWebhookUrl;

    @Value("${dingtalk.alert.enabled:true}")
    private boolean alertEnabled;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 发送Ceph压力预警消息
     * 
     * @param clusterCode 集群编号
     * @param pressure 压力值
     */
    public void sendCephPressureAlert(String clusterCode, Double pressure) {
        if (!alertEnabled) {
            log.info("钉钉预警功能已关闭，跳过发送预警消息");
            return;
        }

        if (dingTalkWebhookUrl == null || dingTalkWebhookUrl.trim().isEmpty()) {
            log.warn("钉钉Webhook URL未配置，无法发送预警消息");
            return;
        }

        try {
            String message = buildCephPressureAlertMessage(clusterCode, pressure);
            sendDingTalkMessage(message);
            log.info("Ceph压力预警消息发送成功: clusterCode={}, pressure={}%", clusterCode, pressure);
        } catch (Exception e) {
            log.error("发送Ceph压力预警消息失败: clusterCode={}, pressure={}%", clusterCode, pressure, e);
        }
    }

    /**
     * 构建Ceph压力预警消息内容
     */
    private String buildCephPressureAlertMessage(String clusterCode, Double pressure) {
        StringBuilder message = new StringBuilder();
        
        // 添加警告emoji和标题
        message.append("🚨 **Ceph集群压力预警** 🚨\n\n");
        
        // 添加详细信息
        message.append("📊 **集群信息**\n");
        message.append("- 集群编号: `").append(clusterCode).append("`\n");
        message.append("- 当前压力: <font color=\"#FF0000\">**").append(String.format("%.2f", pressure)).append("%**</font>\n");
        message.append("- 预警阈值: `80.00%`\n");
        message.append("- 预警时间: `").append(DATE_FORMAT.format(new Date())).append("`\n\n");
        
        // 添加状态指示
        if (pressure >= 95) {
            message.append("🔴 **危险级别**: 极高！！！\n");
        } else if (pressure >= 90) {
            message.append("🟠 **警告级别**: 高！\n");
        } else {
            message.append("🟡 **注意级别**: 偏高.\n");
        }
        
//        // 添加建议操作
//        message.append("\n💡 **建议操作**\n");
//        message.append("- 检查集群存储使用情况\n");
//        message.append("- 清理不必要的数据\n");
//        message.append("- 考虑扩容存储资源\n");
//        message.append("- 监控集群性能指标\n\n");
        
        // 添加联系信息
//        message.append("📞 如需技术支持，请联系运维团队");
        
        return message.toString();
    }

    /**
     * 发送钉钉消息
     */
    private void sendDingTalkMessage(String message) {
        try {
            // 构建钉钉消息体
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msgtype", "markdown");
            
            Map<String, Object> markdown = new HashMap<>();
            markdown.put("title", "Ceph集群压力预警");
            markdown.put("text", message);
            messageBody.put("markdown", markdown);
            
            // 设置@所有人（可选）
            Map<String, Object> at = new HashMap<>();
            at.put("isAtAll", false);
            messageBody.put("at", at);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 发送请求
            HttpEntity<String> request = new HttpEntity<>(JSONObject.toJSONString(messageBody), headers);
            ResponseEntity<String> response = restTemplate.postForEntity(dingTalkWebhookUrl, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("钉钉消息发送成功: {}", response.getBody());
            } else {
                log.error("钉钉消息发送失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("发送钉钉消息异常", e);
            throw e;
        }
    }

    /**
     * 测试钉钉消息发送
     */
    public void sendTestMessage() {
        if (!alertEnabled) {
            log.info("钉钉预警功能已关闭");
            return;
        }
        
        try {
            String testMessage = "🧪 **钉钉消息测试**\n\n" +
                    "这是一条测试消息，用于验证钉钉机器人配置是否正确。\n\n" +
                    "⏰ 发送时间: `" + DATE_FORMAT.format(new Date()) + "`\n\n" +
                    "✅ 如果您收到此消息，说明钉钉机器人配置成功！";
            
            sendDingTalkMessage(testMessage);
            log.info("钉钉测试消息发送完成");
        } catch (Exception e) {
            log.error("发送钉钉测试消息失败", e);
        }
    }
}
