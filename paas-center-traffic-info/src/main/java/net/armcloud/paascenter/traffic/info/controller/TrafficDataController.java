package net.armcloud.paascenter.traffic.info.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.domain.Result;
import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephClusterPressureDTO;
import net.armcloud.paascenter.traffic.info.model.dto.DiskInfoDTO;
import net.armcloud.paascenter.traffic.info.model.dto.TrafficDataDTO;
import net.armcloud.paascenter.traffic.info.service.DingTalkAlertService;
import net.armcloud.paascenter.traffic.info.service.ICephPressureDataService;
import net.armcloud.paascenter.traffic.info.service.IPadTrafficInfoService;
import net.armcloud.paascenter.traffic.info.service.PadTrafficInfoClickHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/traffic-info/open/traffic")
public class TrafficDataController {
    @Autowired
    @Qualifier("padTrafficInfoServiceImpl")
    private IPadTrafficInfoService padTrafficInfoService;

    @Resource
    private TaskExecutor trafficDiskExecutor;

    @Resource
    private TaskExecutor trafficScreenshotExecutor;

    @Resource
    private PadTrafficInfoClickHouseService trafficInfoClickHouseService;

    @Resource
    private ICephPressureDataService cephPressureDataService;

    @Resource
    private DingTalkAlertService dingTalkAlertService;

    @PostMapping("/save")
    @ApiOperation(value = "实例流量数据信息")
    public Result<?> screenshotLocal(@Valid @RequestBody TrafficDataDTO param) {
        trafficScreenshotExecutor.execute(() -> {
            try {
                padTrafficInfoService.sendMessage(param);
            } catch (Exception e) {
                log.error("实例流量数据信息", e);
            }
        });
        return Result.ok();
    }


    /**
     * cbs上报磁盘信息
     */
    @PostMapping("/disk/info")
    public Result<?> diskInfo(@Valid @RequestBody DiskInfoDTO dto) {
        log.info("cbs上报磁盘信息:{}", JSONObject.toJSONString(dto));
        //集合超过设置大小直接丢弃
        if ((CollectionUtil.isNotEmpty(dto.getTop10CpuInfo()) && dto.getTop10CpuInfo().size() > 10)
                || (CollectionUtil.isNotEmpty(dto.getTop10IOInfo()) && dto.getTop10IOInfo().size() > 30)) {
            log.info("cbs上报磁盘信息_丢弃:{}", JSONObject.toJSONString(dto));
            return Result.ok();
        }
        trafficDiskExecutor.execute(() -> {
            try {
                padTrafficInfoService.reportDiskInfo(dto);
            } catch (Exception e) {
                log.error("cbs上报磁盘信息失败", e);
            }
        });
        return Result.ok();
    }

    @PostMapping("/cbs/resource/report")
    public Result<?> resourceReport(@Valid @RequestBody JSONObject json) {
        log.info("cbs资源使用信息:{}", json);
        return Result.ok();

    }

    @PostMapping("/edge/ceph/report")
    @ApiOperation(value = "Ceph压力数据上报")
    public Result<Void> cephReport(@Valid @RequestBody JSONObject json) {
        log.info("ceph压力数据上报:{}", json);
        try {
            CephPressureReportDTO requestDTO = json.toJavaObject(CephPressureReportDTO.class);
            Boolean result = cephPressureDataService.saveCephPressureData(requestDTO);
            if (result) {
                log.info("Ceph压力数据保存成功: clusterCode={}", requestDTO.getClusterCode());
                return Result.ok();
            } else {
                log.warn("Ceph压力数据保存失败: {}", requestDTO);
                return Result.fail("数据保存失败");
            }
        } catch (Exception e) {
            log.error("Ceph压力数据上报处理异常", e);
            return Result.fail("数据处理异常");
        }
    }

    @PostMapping("/edge/ceph/chart")
    @ApiOperation(value = "查询Ceph压力折线图数据")
    public Result<List<CephPressureChartDTO>> getCephPressureChart(@RequestBody CephPressureQueryDTO queryDTO) {
        log.info("查询Ceph压力折线图数据: {}", queryDTO);
        // 校验参数
        if (queryDTO == null || queryDTO.getStartTime() == null || queryDTO.getEndTime() == null) {
            log.warn("查询参数不完整: {}", queryDTO);
            return Result.fail("查询参数不完整");
        }
        try {
            List<CephPressureChartDTO> chartData = cephPressureDataService.getChartData(queryDTO);
            log.info("查询到{}条Ceph压力数据", chartData.size());
            return Result.ok(chartData);
        } catch (Exception e) {
            log.error("查询Ceph压力折线图数据异常", e);
            return Result.fail("查询数据异常");
        }
    }

    @GetMapping("/edge/ceph/realtime/{clusterCode}")
    @ApiOperation(value = "根据集群编号查询实时压力数据")
    public Result<CephClusterPressureDTO> getRealTimePressure(@PathVariable String clusterCode) {
        log.info("查询集群{}实时压力数据", clusterCode);

        if (StringUtils.isBlank(clusterCode)) {
            log.warn("集群编号不能为空");
            return Result.fail("集群编号不能为空");
        }

        try {
            CephClusterPressureDTO pressureData = cephPressureDataService.getRealTimePressure(clusterCode);
            if (pressureData != null) {
                log.info("查询到集群{}实时压力数据: {}%", clusterCode, pressureData.getCurrentPressure());
                return Result.ok(pressureData);
            } else {
                log.warn("未找到集群{}的实时压力数据", clusterCode);
                return Result.fail("未找到该集群的实时压力数据");
            }
        } catch (Exception e) {
            log.error("查询集群{}实时压力数据异常", clusterCode, e);
            return Result.fail("查询数据异常");
        }
    }

    @GetMapping("/edge/ceph/realtime/all")
    @ApiOperation(value = "查询所有集群的实时压力数据")
    public Result<List<CephClusterPressureDTO>> getAllRealTimePressure() {
        log.info("查询所有集群实时压力数据");

        try {
            List<CephClusterPressureDTO> pressureDataList = cephPressureDataService.getAllRealTimePressure();
            log.info("查询到{}个集群的实时压力数据", pressureDataList.size());
            return Result.ok(pressureDataList);
        } catch (Exception e) {
            log.error("查询所有集群实时压力数据异常", e);
            return Result.fail("查询数据异常");
        }
    }

}
