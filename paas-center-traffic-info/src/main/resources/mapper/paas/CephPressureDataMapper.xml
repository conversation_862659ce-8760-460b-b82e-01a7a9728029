<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper">

    <!-- 原始数据结果映射 -->
    <resultMap id="RawDataResultMap" type="net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorRawData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="metric_type" property="metricType" jdbcType="VARCHAR"/>
        <result column="metric_value" property="metricValue" jdbcType="DOUBLE"/>
        <result column="metric_unit" property="metricUnit" jdbcType="VARCHAR"/>
        <result column="report_time" property="reportTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分钟聚合数据结果映射 -->
    <resultMap id="MinuteDataResultMap" type="net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorMinuteData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="metric_type" property="metricType" jdbcType="VARCHAR"/>
        <result column="minute_time" property="minuteTime" jdbcType="TIMESTAMP"/>
        <result column="avg_value" property="avgValue" jdbcType="DOUBLE"/>
        <result column="max_value" property="maxValue" jdbcType="DOUBLE"/>
        <result column="min_value" property="minValue" jdbcType="DOUBLE"/>
        <result column="data_count" property="dataCount" jdbcType="INTEGER"/>
        <result column="metric_unit" property="metricUnit" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 从原始表查询分钟维度数据（时间跨度<=30分钟） -->
    <select id="selectMinuteDataFromRaw" resultType="net.armcloud.paascenter.traffic.info.model.dto.ClusterMonitorChartDataDTO">
        SELECT
            cluster_code as clusterCode,
            max(cluster_name) as clusterName,
            STR_TO_DATE(DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00'), '%Y-%m-%d %H:%i:%s') as timePoint,
            AVG(metric_value) as avgValue,
            MAX(metric_value) as maxValue,
            MIN(metric_value) as minValue,
            COUNT(*) as dataCount,
            MAX(metric_unit) as metricUnit
        FROM cluster_monitor_raw_data a
        inner join edge_cluster b on a.cluster_code = b.cluster_code
        WHERE 1=1
        <if test="clusterCode != null and clusterCode != ''">
            AND cluster_code = #{clusterCode}
        </if>
        AND metric_type = #{metricType}
        AND report_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY cluster_code, DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
        ORDER BY cluster_code, timePoint ASC
    </select>

    <!-- 从分钟聚合表查询数据（时间跨度>30分钟） -->
    <select id="selectMinuteDataFromAgg" resultType="net.armcloud.paascenter.traffic.info.model.dto.ClusterMonitorChartDataDTO">
        SELECT
            cluster_code as clusterCode,
            max(cluster_name) as clusterName,
            minute_time as timePoint,
            avg_value as avgValue,
            max_value as `maxValue`,
            min_value as `minValue`,
            data_count as dataCount,
            metric_unit as metricUnit
        FROM cluster_monitor_minute_data a
        inner join edge_cluster b on a.cluster_code = b.cluster_code
        WHERE 1=1
        <if test="clusterCode != null and clusterCode != ''">
            AND cluster_code = #{clusterCode}
        </if>
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY cluster_code, minute_time ASC
    </select>

    <!-- 从分钟聚合表查询聚合数据（当数据量超过1000时使用） -->
    <select id="selectAggregatedDataFromAgg" resultType="net.armcloud.paascenter.traffic.info.model.dto.ClusterMonitorChartDataDTO">
        SELECT
            cluster_code as clusterCode,
            max(cluster_name) as clusterName,
            STR_TO_DATE(DATE_FORMAT(
                DATE_SUB(minute_time, INTERVAL MINUTE(minute_time) % #{intervalMinutes} MINUTE),
                '%Y-%m-%d %H:%i:00'
            ), '%Y-%m-%d %H:%i:%s') as timePoint,
            AVG(avg_value) as avgValue,
            MAX(max_value) as `maxValue`,
            MIN(min_value) as `minValue`,
            SUM(data_count) as dataCount,
            MAX(metric_unit) as metricUnit
        FROM cluster_monitor_minute_data a
        inner join edge_cluster b on a.cluster_code = b.cluster_code
        WHERE 1=1
        <if test="clusterCode != null and clusterCode != ''">
            AND cluster_code = #{clusterCode}
        </if>
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY cluster_code, DATE_FORMAT(
            DATE_SUB(minute_time, INTERVAL MINUTE(minute_time) % #{intervalMinutes} MINUTE),
            '%Y-%m-%d %H:%i:00'
        )
        ORDER BY cluster_code, timePoint ASC
    </select>

    <!-- 统计分钟聚合表中指定时间区间内的数据条数 -->
    <select id="countMinuteDataByTimeRange" resultType="int">
        SELECT COUNT(*)
        FROM cluster_monitor_minute_data
        WHERE cluster_code = #{clusterCode}
        AND metric_type = #{metricType}
        AND minute_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 删除原始表中指定时间之前的数据（保留1个月） -->
    <delete id="deleteRawDataBeforeTime">
        DELETE FROM cluster_monitor_raw_data
        WHERE report_time &lt; #{beforeTime}
    </delete>

    <!-- 删除分钟聚合表中指定时间之前的数据（保留3个月） -->
    <delete id="deleteMinuteDataBeforeTime">
        DELETE FROM cluster_monitor_minute_data
        WHERE minute_time &lt; #{beforeTime}
    </delete>

    <!-- 实时聚合单条数据到分钟表 -->
    <insert id="aggregateSingleDataToMinute">
        INSERT INTO cluster_monitor_minute_data (
            cluster_code,
            metric_type,
            minute_time,
            avg_value,
            max_value,
            min_value,
            data_count,
            metric_unit,
            create_time
        ) VALUES (
            #{clusterCode},
            #{metricType},
            DATE_FORMAT(#{reportTime}, '%Y-%m-%d %H:%i:00'),
            #{metricValue},
            #{metricValue},
            #{metricValue},
            1,
            #{metricUnit},
            NOW()
        ) ON DUPLICATE KEY UPDATE
            avg_value = round((avg_value * data_count + #{metricValue}) / (data_count + 1),2),
            max_value = GREATEST(max_value, #{metricValue}),
            min_value = LEAST(min_value, #{metricValue}),
            data_count = data_count + 1,
            metric_unit = #{metricUnit},
            update_time = NOW()
    </insert>

</mapper>
